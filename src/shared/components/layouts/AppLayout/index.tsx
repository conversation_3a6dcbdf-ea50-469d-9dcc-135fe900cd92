'use client';

import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import React, { useEffect } from 'react';
import { openNineDotPanel } from '@shared/stores/nineDotPanelStore';
import useRegisterPushNotificationToken from '@shared/utils/hooks/useRegisterPushNotificationToken';
import ChatPanelSkeleton from 'shared/components/layouts/AppLayout/AppLayout.ChatPanel/partials/ClosedRightPanelItems.loading';
import Header from 'shared/components/layouts/AppLayout/AppLayout.Header';
import LeftNavigation from 'shared/components/layouts/AppLayout/AppLayout.LeftNavigation';
import { MAIN_CENTER_WRAPPER_ID } from 'shared/constants/enums';
import eventKeys from 'shared/constants/event-keys';
import { mutableStore } from 'shared/constants/mutableStore';
import { useAuthState } from 'shared/contexts/Auth/auth.provider';
import {
  useGlobalDispatch,
  useGlobalState,
} from 'shared/contexts/Global/global.provider';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import useIsPagePublished from 'shared/hooks/useIsPagePublished';
import Drawer from 'shared/uikit/Drawer';
import Flex from 'shared/uikit/Flex';
import Media from 'shared/uikit/Media';
import cnj from 'shared/uikit/utils/cnj';
import { routeNames } from 'shared/utils/constants/routeNames';
import useLocation from 'shared/utils/hooks/useLocation';
import event from 'shared/utils/toolkit/event';
import useScrollToTopEventListener from 'shared/utils/useScrollToTopEventListener';
import classes from './index.module.scss';

const ChatPanel = dynamic(
  () => import('shared/components/layouts/AppLayout/AppLayout.ChatPanel'),
  {
    ssr: false,
    loading: () => <ChatPanelSkeleton />,
  }
);

const Bottombar = dynamic(
  () => import('shared/components/layouts/AppLayout/AppLayout.Bottombar'),
  {
    ssr: false,
  }
);

const AppLayout: React.FC<React.PropsWithChildren> = ({ children }) => {
  const { searchParams, pathname, preserveSearchParams } = useLocation();
  const { replace } = useRouter();
  const { reFetchAppObject, authUser } = useGetAppObject();
  const isPagePublished = useIsPagePublished();
  const globalDispatch = useGlobalDispatch();
  const isOpenLeftPanel = useGlobalState('isOpenLeftPanel');
  const isLoggedIn = useAuthState('isLoggedIn');

  const isUnAuthAvailability =
    !isLoggedIn && pathname?.includes(routeNames.schedulesAvailability.main);

  useScrollToTopEventListener();
  useRegisterPushNotificationToken();

  useEffect(() => {
    if (!authUser?.publicSetting) {
      reFetchAppObject();
    }
  }, [authUser]);

  const closeLeftPanel = () => {
    globalDispatch({
      type: 'CLOSE_LEFT_PANEL',
    });
  };
  useEffect(() => {
    event.trigger(eventKeys.scrollToTopFeedList);
    globalDispatch({
      type: 'RESET_ON_NAVIGATION',
    });
    closeLeftPanel();
  }, [pathname]);

  const openTarget = searchParams.get('open-target');
  useEffect(() => {
    if (openTarget === 'ticket') {
      openNineDotPanel({ isOpen: true, defaultActiveStep: 'SUPPORT' });
      replace(preserveSearchParams(pathname, { delete: ['open-target'] }));
    }
  }, [openTarget]);

  const toggleLeftPanelHandler = () => {
    globalDispatch({
      type: isOpenLeftPanel ? 'CLOSE_LEFT_PANEL' : 'OPEN_LEFT_PANEL',
    });
  };

  const visibleChatPanel =
    isLoggedIn && pathname !== routeNames.messages && isPagePublished;

  mutableStore.hasMessagePanelOnRightSide = visibleChatPanel;

  return (
    <Flex className={classes.appRoot}>
      <Header toggleLeftPanel={toggleLeftPanelHandler} />
      <Flex
        className={cnj(
          classes.appMain,
          isUnAuthAvailability && classes.noMarginTop
        )}
      >
        {isLoggedIn && isPagePublished && (
          <Flex className={classes.mainLeft}>
            <LeftNavigation closeLeftPanel={closeLeftPanel} autoWide narrow />
          </Flex>
        )}
        <Flex
          id={MAIN_CENTER_WRAPPER_ID}
          className={cnj(
            classes.mainCenter,
            visibleChatPanel && classes.mainCenterLoggedIn
          )}
        >
          {children}
        </Flex>
        {visibleChatPanel && (
          <Flex
            className={cnj(classes.mainRight, isOpenLeftPanel && classes.dark)}
          >
            <Media greaterThan="tablet">
              <ChatPanel />
            </Media>
          </Flex>
        )}
      </Flex>
      {isOpenLeftPanel && (
        <Drawer
          sliderDir="left"
          onBackDropClick={closeLeftPanel}
          contentClassName={classes.leftDrawerContent}
        >
          <Media greaterThan="tablet">
            <LeftNavigation
              closeLeftPanel={closeLeftPanel}
              narrow={false}
              wide
            />
          </Media>
        </Drawer>
      )}
      {isLoggedIn && (
        <Media at="tablet">
          <Bottombar />
        </Media>
      )}
    </Flex>
  );
};
export default AppLayout;
