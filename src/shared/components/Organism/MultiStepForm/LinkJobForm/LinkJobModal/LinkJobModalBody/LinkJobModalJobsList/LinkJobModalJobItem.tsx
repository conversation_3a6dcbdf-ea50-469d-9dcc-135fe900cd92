import React from 'react';
import ItemComponent from '@shared/components/Organism/AsyncPickerModal/components/ItemComponent';
import Icon from '@shared/uikit/Icon';
import Typography from '@shared/uikit/Typography';
import CheckBox from 'shared/uikit/CheckBox';
import Flex from 'shared/uikit/Flex';
import type { IJobApi } from '@shared/types/job';
import type { ProjectProps } from '@shared/types/project';

interface ILinkJobModalJobItemProps {
  job: any;
  onClick?: (job: any) => void;
  selectedJobs?: IJobApi[];
  action?: React.ReactNode;
  subtitle?: React.ReactNode;
}

const LinkJobModalJobItem: React.FunctionComponent<
  ILinkJobModalJobItemProps
> = (props) => {
  const { job, onClick, selectedJobs, action, subtitle } = props;

  return (
    <ItemComponent
      key={job.id}
      title={job.title}
      subTitle={
        subtitle ?? (
          <Flex className="!flex-row items-center gap-4 mt-4">
            <Icon
              name="projects-light"
              type="far"
              size={16}
              color="secondaryDisabledText"
            />
            <Typography color="secondaryDisabledText" size={14} height={16}>
              {job.projects.map((proj: ProjectProps) => proj.title)?.join(', ')}
            </Typography>
          </Flex>
        )
      }
      image={job.pageCroppedImageUrl}
      onClick={() => onClick?.(job)}
      isCompany
    >
      {action ?? (
        <CheckBox value={selectedJobs?.some((_job) => _job.id === job.id)} />
      )}
    </ItemComponent>
  );
};

export default LinkJobModalJobItem;
