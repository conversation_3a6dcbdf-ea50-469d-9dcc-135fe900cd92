import React from 'react';
import ItemComponent from '@shared/components/Organism/AsyncPickerModal/components/ItemComponent';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import IconButton from '@shared/uikit/Button/IconButton';
import CheckBox from '@shared/uikit/CheckBox';
import { SearchableAsyncList } from '@shared/uikit/SearchableAsyncList';
import useTranslation from '@shared/utils/hooks/useTranslation';
import SkeletonItems from './SubmitToClientSkeletonItems';
import useSubmitToClientForm from './useSubmitToClientForm';

const StepsList = () => {
  const { t } = useTranslation();

  const {
    stepKey: step,
    apiFunc,
    apiParams,
    placeHolder,
    queryName,
    onClickJob,
    onClickSuggestCompany,
    submitToClientFormState,
  } = useSubmitToClientForm();

  const content = (item: any) =>
    step === 'client' ? (
      <ItemComponent
        key={item?.id}
        image={item?.imageUrl}
        title={item?.title}
        subTitle={item?.industryName}
      >
        <IconButton
          name="chevron-right"
          size="md20"
          color="brand"
          onClick={() => onClickSuggestCompany(item)}
          style={{ cursor: 'pointer' }}
        />
      </ItemComponent>
    ) : (
      <ItemComponent
        key={item?.id}
        image={item?.imageUrl}
        title={item?.title}
        subTitle={item?.subtitle}
        onClick={() => onClickJob(item)}
      >
        <CheckBox
          value={submitToClientFormState?.jobIds?.find((id) => id === item.id)}
        />
      </ItemComponent>
    );

  return (
    <SearchableAsyncList
      name={queryName}
      variant="multi"
      params={apiParams}
      enableInfiniteScroll
      listItemsClassName="py-16"
      renderLoading={<SkeletonItems />}
      pageSize={20}
      renderEmpty={
        <EmptySearchResult
          title={t(emptyTitleText[step === 'client' ? 'client' : 'job'])}
          sectionMessage={t(
            emptyDescriptionText[step === 'client' ? 'client' : 'job']
          )}
          className="flex-1"
        />
      }
      renderItem={({ item }) => content(item)}
      apiFunc={apiFunc}
      hasSearch
      normalizer={(values) => values?.content || values}
      placeholder={placeHolder}
    />
  );
};

export default StepsList;

const emptyTitleText = {
  client: 'no_clients_found',
  job: 'no_jobs_found',
};
const emptyDescriptionText = {
  client: 'no_client_to_submit',
  job: 'no_job_to_link',
};
