import { useFormikContext } from 'formik';
import React from 'react';
import ItemComponent from '@shared/components/Organism/AsyncPickerModal/components/ItemComponent';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import CheckBox from '@shared/uikit/CheckBox';
import { SearchableAsyncList } from '@shared/uikit/SearchableAsyncList';
import { getCandidatesList } from '@shared/utils/api/candidates';
import { QueryKeys } from '@shared/utils/constants';
import useTranslation from '@shared/utils/hooks/useTranslation';
import useCreateTodoForm from '../../useCreateNoteForm';
import SkeletonItems from './CreateNoteSkeletonItem';

const CreateNoteStepOne = () => {
  const { t } = useTranslation();

  const { listApiParams, onClickCandidate } = useCreateTodoForm('list');
  const { values, setFieldValue } = useFormikContext<{
    selectedCandidates: number[];
  }>();

  const content = (item: any) => (
    <ItemComponent
      key={item?.id}
      image={item?.profile?.imageUrl}
      title={item?.profile?.fullName}
      subTitle={item?.profile?.usernameAtSign}
      onClick={() => onClickCandidate(item, values, setFieldValue)}
      isCompany={false}
    >
      <CheckBox
        value={values?.selectedCandidates?.find((id: number) => id === item.id)}
      />
    </ItemComponent>
  );

  return (
    <SearchableAsyncList
      name={QueryKeys.getCandidatesList}
      variant="multi"
      params={listApiParams}
      enableInfiniteScroll
      listItemsClassName="py-16"
      renderLoading={<SkeletonItems />}
      renderNextPageLoading={<SkeletonItems />}
      pageSize={20}
      renderEmpty={
        <EmptySearchResult
          title={t('no_candidates_found')}
          sectionMessage={t('no_candidates_to_link')}
          className="flex-1"
        />
      }
      renderItem={({ item }) => content(item)}
      apiFunc={getCandidatesList}
      hasSearch
      placeholder={t('search_candidates')}
    />
  );
};

export default CreateNoteStepOne;
