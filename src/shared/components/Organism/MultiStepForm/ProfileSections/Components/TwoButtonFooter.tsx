import eventKeys from 'shared/constants/event-keys';
import Button from 'shared/uikit/Button';
import Flex from 'shared/uikit/Flex';
import SubmitButton from 'shared/uikit/Form/SubmitButton';
import useTranslation from 'shared/utils/hooks/useTranslation';
import event from 'shared/utils/toolkit/event';
import classes from './TwoButtonFooter.module.scss';

type Props = {
  submitLabel?: string;
  secondaryButtonLabel?: string;
  secondaryButtonOnClick?: () => any;
  onSubmitClick?: () => any;
  disabledSubmit?: boolean;
  isSubmitLoading?: boolean;
};

export const TwoButtonFooter: React.FC<Props> = ({
  submitLabel,
  secondaryButtonLabel,
  secondaryButtonOnClick,
  onSubmitClick,
  disabledSubmit,
  isSubmitLoading,
}) => {
  const { t } = useTranslation();
  const ButtonTag = onSubmitClick ? Button : SubmitButton;

  return (
    <Flex className={classes.footer}>
      <Button
        fullWidth
        label={secondaryButtonLabel || t('discard')}
        schema="gray"
        onClick={
          secondaryButtonOnClick || (() => event.trigger(eventKeys.closeModal))
        }
      />
      <ButtonTag
        fullWidth
        label={submitLabel || t('update')}
        schema="primary-blue"
        onClick={onSubmitClick}
        disabled={disabledSubmit}
        isLoading={isSubmitLoading}
      />
    </Flex>
  );
};
