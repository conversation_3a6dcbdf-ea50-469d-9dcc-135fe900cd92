import React, { type MouseEvent } from 'react';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import useTranslation from '@shared/utils/hooks/useTranslation';
import BillingCard from './Partials/BillingCard';
import { type Billing } from './types';

interface BillingDetailsProps {
  data: Billing[];
  onBack?: (event?: MouseEvent<any> | null) => void;
}

export default function BillingDetails({ data, onBack }: BillingDetailsProps) {
  const { t } = useTranslation();

  return (
    <FixedRightSideModal
      onClose={onBack}
      onBack={onBack}
      onClickOutside={onBack as any}
    >
      <ModalHeaderSimple
        backButtonProps={{
          onClick: onBack,
        }}
        title={t('billing_details')}
        hideBack={false}
        noCloseButton
      />
      <ModalBody className="gap-20">
        {data?.map((item, idx) => (
          <BillingCard data={item} key={`details-${idx}`} />
        ))}
      </ModalBody>
    </FixedRightSideModal>
  );
}
