import { useParams } from 'next/navigation';
import { jobsEndpoints, QueryKeys } from '@shared/utils/constants';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import { sortPipelinesNormalizer } from '@shared/utils/normalizers/pipelinesNormalizer';
import type { StageOption } from './types';
import type { JobPiplineData } from '@shared/types/pipelineProps';

export const useStageOptions = (): StageOption[] => {
  const params = useParams();
  const id = params?.id as string;

  const { data } = useReactQuery<JobPiplineData>({
    action: {
      key: [QueryKeys.getPipeline, id],
      url: jobsEndpoints.getPipeline(id),
      beforeCache: (innerData: any) => sortPipelinesNormalizer(innerData),
    },
  });

  if (!data?.pipelines?.length) return [];

  return data?.pipelines?.map((item) => ({
    id: item?.id,
    value: item?.type,
    label: item?.title,
    icon: 'circle-s',
    iconColor: item?.color,
  }));
};
