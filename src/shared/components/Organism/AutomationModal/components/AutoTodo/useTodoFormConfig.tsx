import { useMemo } from 'react';
import AvatarCard from '@shared/uikit/AvatarCard';
import Icon from '@shared/uikit/Icon';
import Tooltip from '@shared/uikit/Tooltip';
import { pageEndpoints } from '@shared/utils/constants';
import useTranslation from '@shared/utils/hooks/useTranslation';

export const useTodoFormConfig = () => {
  const { t } = useTranslation();

  return useMemo(
    () => [
      {
        name: 'title',
        cp: 'input',
        label: t('Title'),
        maxLength: 30,
        required: true,
        wrapStyle: '!mb-4',
      },
      {
        name: 'assigneeUserId',
        cp: 'avatarAsyncAutoComplete',
        label: t('Assignee'),
        visibleRightIcon: true,
        visibleOptionalLabel: false,
        forceVisibleError: false,
        required: true,
        wrapStyle: '!mb-4',
        url: pageEndpoints.pageAccessibilities,
        renderItem: ({ item }: any) => (
          <AvatarCard
            containerProps={{
              className: 'itemWrapper',
            }}
            data={{
              title: item.label,
              image: item.image,
              subTitle: item.username,
            }}
            action={
              item?.isLock && (
                <Tooltip
                  placement="left"
                  trigger={<Icon name="lock" type="fal" size={18} />}
                >
                  {t('private')}
                </Tooltip>
              )
            }
          />
        ),
        normalizer: (data: any) =>
          data?.content?.map((item) => ({
            value: item?.profileInfo?.userId,
            label: item?.profileInfo.fullName,
            image: item?.profileInfo?.croppedImageUrl,
            job: item?.profileInfo.occupationName,
            username: `@${item?.profileInfo.username}`,
            isPrivate: !item?.profileInfo?.allowPageRoleAssign,
            id: item?.profileInfo?.userId,
          })),
      },
      {
        name: 'description',
        cp: 'richtext',
        label: t('Description'),
        required: true,
        className: '!min-h-[120px]',
        wrapStyle: '!mb-4',
      },
      {
        name: 'attachments',
        cp: 'attachmentPicker',
        label: t('Attachments'),
        type: 'imgAndPdf',
        wrapStyle: '!mb-4',
      },
    ],
    [t]
  );
};
