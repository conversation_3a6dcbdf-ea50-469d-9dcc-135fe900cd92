import React from 'react';
import { PrivateAttachmentsList } from '@shared/components/molecules/Attachments';
import { UserProfile } from '@shared/components/Organism/AutomationModal/components/UserProfile';
import Divider from '@shared/uikit/Divider';
import Flex from '@shared/uikit/Flex';
import Box from '@shared/uikit/Layout/Box';
import { RichTextView } from '@shared/uikit/RichText';
import Typography from '@shared/uikit/Typography';
import useTranslation from 'shared/utils/hooks/useTranslation';

interface TodoDisplayProps {
  todo: any;
}

export const TodoDisplay: React.FC<TodoDisplayProps> = ({ todo }) => {
  const { t } = useTranslation();

  return (
    <Flex className="w-full p-20 ">
      <Box className="bg-gray_5 rounded-lg !px-20 !pb-12 !pt-20 shadow-sm border border-solid border-techGray_10">
        {todo?.title && (
          <Typography className="mb-4 !text-smoke_coal !font-bold !text-lg">
            {todo?.title}
          </Typography>
        )}
        {todo?.text && (
          <RichTextView
            html={todo?.text}
            typographyProps={{
              size: 14,
              color: 'smoke_coal',
            }}
            showMore={false}
          />
        )}
        <Divider className="my-[12px]" />
        <Flex className="gap-0">
          <Flex>
            <Typography className="!text-[13px] !font-medium !text-colorIconForth2 mb-6">
              {t('Assignee')}
            </Typography>
            <UserProfile
              croppedImageUrl={todo?.assigneeUser?.croppedImageUrl}
              username={todo?.assigneeUser?.username}
              role={todo?.assigneeUser?.occupationName}
              variant="card"
            />
          </Flex>

          <Flex>
            <Typography className="!text-[13px] !font-medium !text-colorIconForth2 mb-6">
              {t('Creator')}
            </Typography>
            <UserProfile
              croppedImageUrl={todo?.user?.croppedImageUrl}
              username={todo?.user?.username}
              role={todo?.user?.occupationName}
              variant="card"
            />
          </Flex>
        </Flex>

        {/* Attachments */}
        {todo?.fileIds && todo.fileIds.length > 0 && (
          <PrivateAttachmentsList
            ids={todo.fileIds.map((id: any) => Number(id))}
          />
        )}
      </Box>
    </Flex>
  );
};
