import { useMutation } from '@tanstack/react-query';
import AutoConditionalModal from '@shared/components/Organism/AutomationModal/components/AutoConditionalModal/AutoConditionalModal';
import { useMultiStepFormState } from '@shared/hooks/useMultiStepForm';
import useResponseToast from '@shared/hooks/useResponseToast';
import {
  deletePipelineAutoRejection,
  getPipelineAutoRejection,
  putPipelineAutoRejection,
} from '@shared/utils/api/pipeline';
import { QueryKeys } from '@shared/utils/constants';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';

export function AutoRejectRequirementsModal() {
  const automationState = useMultiStepFormState('automation');
  const pipelineId = Number((automationState?.data as any)?.id);
  const { t } = useTranslation();
  const { handleSuccess } = useResponseToast();

  const {
    data: autoRejectData,
    isLoading: isLoadingAutoReject,
    refetch: refetchAutoReject,
  } = useReactQuery({
    action: {
      apiFunc: () => getPipelineAutoRejection(pipelineId),
      key: [QueryKeys.getPipelineAutoRejection, pipelineId],
    },
  });

  const { mutate: updateAutoRejectMutation, isPending: isLoadingUpdate } =
    useMutation({
      mutationFn: putPipelineAutoRejection,
      onSuccess: () => {
        handleSuccess({
          message: t('requirements_set_on_auto_reject_subtitle'),
          title: t('requirements_set_on_auto_reject'),
        });
        refetchAutoReject();
      },
    });

  const {
    mutate: deleteAutoRejectMutation,
    isPending: isLoadingDeleteAutoReject,
  } = useMutation({
    mutationFn: deletePipelineAutoRejection,
    onSuccess: () => {
      refetchAutoReject();
    },
  });

  return (
    <AutoConditionalModal
      autoConditionData={autoRejectData}
      isLoadingAutoCondition={isLoadingAutoReject}
      updateAutoConditional={updateAutoRejectMutation}
      deleteAutoConditionalMutation={deleteAutoRejectMutation}
      isLoadingDeleteAutoConditional={isLoadingDeleteAutoReject}
      automationType="autoReject"
      headerTitle={t('move_to')}
      isLoadingUpdate={isLoadingUpdate}
    />
  );
}
