import type { ReactNode } from 'react';

export interface ActionOption {
  value: string;
  label: string;
}

export interface TypeOption {
  value: string;
  label: string;
}

export interface RequirementBox {
  id: string;
  action: ActionOption | null;
  type: TypeOption | null;
  value: any;
}

export interface StageOption {
  value: string;
  id: string;
  label: string;
  icon: string;
  iconColor: string;
}

export interface FormValues {
  [key: string]: any;
}

export interface AutoMovementData {
  toPipelineId: number;
  coverLetterCheckingEnabled: boolean;
  coverLetterPresentedOrNot: boolean;
  phoneNumberCheckingEnabled: boolean;
  phoneNumberPresentedOrNot: boolean;
  ageRangeCheckingEnabled: boolean;
  ageRangeIsBetweenOrNot: boolean;
  minAge: number;
  maxAge: number;
  locationCheckingEnabled: boolean;
  locationCountriesContainsOrNot: boolean;
  locationCountriesIs: {
    id: number;
    name: string;
    code: string;
  }[];
}

export interface NoteFormConfig {
  name: string;
  cp: string;
  label: string;
  required?: boolean;
  variant?: string;
  className?: string;
  labelProps?: Record<string, any>;
  type?: string;
  formGroup?: Record<string, any>;
  options?: Array<{ label: string; value: string; hint?: { content: string } }>;
  rightIcon?: React.ReactNode;
}
