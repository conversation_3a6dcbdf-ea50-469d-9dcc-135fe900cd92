'use client';

import React, { useState } from 'react';
import { useAutoConditionalStepOne } from '@shared/components/Organism/AutomationModal/components/AutoConditionalModal/useAutoConditionalStepOne';
import { useAutoConditionalStepThree } from '@shared/components/Organism/AutomationModal/components/AutoConditionalModal/useAutoConditionalStepThree';
import { useAutoConditionalStepTwo } from '@shared/components/Organism/AutomationModal/components/AutoConditionalModal/useAutoConditionalStepTwo';
import MultiStepForm from '@shared/components/Organism/MultiStepForm/MultiStepForm';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import getStepData from '@shared/utils/getStepData';
import type { TemplateFormData } from '@shared/components/Organism/AutomationModal/types/template.types';
import type {
  AutoMovementRequest,
  AutoMovementResponse,
} from '@shared/utils/api/jobs';
import type { UseMutateAsyncFunction } from '@tanstack/react-query';

type AutoConditionalModalProps = {
  autoConditionData?: AutoMovementResponse;
  isLoadingAutoCondition?: boolean;
  updateAutoConditional: UseMutateAsyncFunction<
    any,
    unknown,
    {
      pipelineId: number;
      data: AutoMovementRequest;
    },
    unknown
  >;
  deleteAutoConditionalMutation?: UseMutateAsyncFunction<
    any,
    unknown,
    {
      pipelineId: number;
    },
    unknown
  >;
  isLoadingDeleteAutoConditional: boolean;
  automationType: 'autoMove' | 'autoReject';
  headerTitle: string;
  isLoadingUpdate: boolean;
};

const AutoConditionalModal: React.FC<AutoConditionalModalProps> = ({
  updateAutoConditional,
  autoConditionData,
  isLoadingAutoCondition,
  isLoadingDeleteAutoConditional,
  deleteAutoConditionalMutation,
  automationType,
  headerTitle,
  isLoadingUpdate,
}) => {
  const automationState = useMultiStepFormState('automation');
  const pipelineId = Number((automationState?.data as any)?.id);
  const [formData, setFormData] = useState<any>(undefined);
  const [templateFormValue, setTemplateFormValue] = useState<
    TemplateFormData | undefined
  >(undefined);

  const handleFormDataChange = (newFormData: any) => {
    setFormData(newFormData);
  };

  const handleTemplateCreated = (newTemplateId: number) => {
    updateAutoConditional({
      pipelineId,
      data: {
        toPipelineId: formData?.toPipelineId,
        conditionMatchMode: formData?.selectedConditionMatch?.value,
        coverLetterCheckingEnabled:
          formData?.coverLetterCheckingEnabled ?? false,
        coverLetterPresentedOrNot: formData?.coverLetterPresentedOrNot ?? false,
        phoneNumberCheckingEnabled:
          formData?.phoneNumberCheckingEnabled ?? false,
        phoneNumberPresentedOrNot: formData?.phoneNumberPresentedOrNot ?? false,
        ageRangeCheckingEnabled: formData?.ageRangeCheckingEnabled ?? false,
        ageRangeIsBetweenOrNot: formData?.ageRangeIsBetweenOrNot ?? false,
        minAge: formData?.minAge,
        maxAge: formData?.maxAge,
        locationCheckingEnabled: formData?.locationCheckingEnabled ?? false,
        locationCountriesContainsOrNot:
          formData?.locationCountriesContainsOrNot ?? false,
        locationCountriesIs:
          Array.isArray(formData?.locationCountries) &&
          formData.locationCountries.length > 0
            ? formData.locationCountries
            : [],
        templateId: newTemplateId,
      },
    });
  };

  const handleTemplateSelected = (selectedTemplate: TemplateFormData) => {
    setTemplateFormValue(selectedTemplate);
  };

  const stepOneData = useAutoConditionalStepOne({
    onFormDataChange: handleFormDataChange,
    automationType,
    headerTitle,
    autoConditionData,
    isLoadingAutoCondition,
    deleteAutoConditionalMutation,
    isLoadingDeleteAutoConditional,
    pipelineId,
  });

  const stepTwoData = useAutoConditionalStepTwo({
    onTemplateCreated: handleTemplateCreated,
    templateFormValue,
    handleTemplateSelected,
    templateId: autoConditionData?.templateId,
    isLoadingUpdate,
  });

  const stepThreeData = useAutoConditionalStepThree({
    formData,
    onTemplateSelected: handleTemplateSelected,
  });

  const steps = [...stepOneData, ...stepTwoData, ...stepThreeData];

  const onClose = () => closeMultiStepForm('automation');
  const getHeaderProps = getStepData('getHeaderProps', steps);
  const getStepHeaderProps = getStepData('getStepHeaderProps', steps);
  const renderBody = getStepData('renderBody', steps);
  const renderFooter = getStepData('renderFooter', steps);

  return (
    <MultiStepForm
      wide
      initialStep={0}
      initialValues={{}}
      showConfirm={false}
      enableReinitialize
      totalSteps={2}
      getHeaderProps={getHeaderProps}
      renderBody={renderBody}
      renderFooter={renderFooter}
      getStepHeaderProps={getStepHeaderProps}
      onClose={onClose}
      isOpenAnimation
      formName="automation"
    />
  );
};

export default AutoConditionalModal;
