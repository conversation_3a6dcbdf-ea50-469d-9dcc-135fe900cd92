import Flex from '@shared/uikit/Flex';
import Skeleton from '@shared/uikit/Skeleton';

export function AutoConditionalLoading() {
  return (
    <Flex className="gap-20">
      <Skeleton
        style={{
          width: '100%',
          height: 343,
          borderRadius: '12px',
        }}
      />
      <Skeleton
        style={{
          width: '100%',
          height: 343,
          borderRadius: '12px',
        }}
      />
      <Skeleton
        style={{
          width: '100%',
          height: 343,
          borderRadius: '12px',
        }}
      />
      <Skeleton
        style={{
          width: '100%',
          height: 343,
          borderRadius: '12px',
        }}
      />
    </Flex>
  );
}
