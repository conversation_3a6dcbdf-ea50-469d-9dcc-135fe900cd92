import React from 'react';
import { AutomationStageType } from '@shared/components/Organism/AutomationModal/components/AutomationStageType';
import {
  useActionOptions,
  useTypeOptions,
} from '@shared/components/Organism/AutomationModal/components/commonOptions';
import { TwoButtonFooter } from '@shared/components/Organism/MultiStepForm/ProfileSections/Components/TwoButtonFooter';
import {
  closeMultiStepForm,
  openMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import AutoComplete from '@shared/uikit/AutoComplete';
import IconButton from '@shared/uikit/Button/IconButton';
import Flex from '@shared/uikit/Flex';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import Typography from '@shared/uikit/Typography';
import useMedia from '@shared/uikit/utils/useMedia';
import { geoEndpoints } from '@shared/utils/constants';
import useAuthUser from '@shared/utils/hooks/useAuthUser';
import useTranslation from '@shared/utils/hooks/useTranslation';
import geoNormalizer from '@shared/utils/normalizers/geo';
import type { MultiStepFormProps } from '@shared/components/Organism/MultiStepForm/MultiStepForm';
import type { PipelineInfo } from '@shared/types/pipelineProps';
import Skeleton from '@shared/uikit/Skeleton';
import { AutoConditionalLoading } from '@shared/components/Organism/AutomationModal/components/AutoConditionalModal/AutoConditionalLoading';

interface ActionOption {
  value: string;
  label: string;
}

interface StatusOption {
  value: string;
  label: string;
}

interface RequirementBox {
  id: string;
  action: ActionOption | null;
  status: StatusOption | null;
  value: any;
}

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  getStepHeaderProps?: Exclude<
    MultiStepFormProps['getStepHeaderProps'],
    undefined
  >;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

interface UseAutoConditionalStepOneProps {
  onFormDataChange: (formData: any) => void;
  automationType: 'autoMove' | 'autoReject';
  headerTitle: string;
  autoConditionData?: any;
  isLoadingAutoCondition?: boolean;
  deleteAutoConditionalMutation?: any;
  isLoadingDeleteAutoConditional?: boolean;
  pipelineId?: number;
}

export function useAutoConditionalStepOne({
  onFormDataChange,
  automationType,
  headerTitle,
  autoConditionData,
  isLoadingAutoCondition,
  deleteAutoConditionalMutation,
  isLoadingDeleteAutoConditional,
  pipelineId,
}: UseAutoConditionalStepOneProps): SingleDataItem[] {
  const { t } = useTranslation();
  const { isMoreThanTablet } = useMedia();
  const allActionOptions = useActionOptions();
  const typeOptions = useTypeOptions();

  const getFilteredActionOptions = (
    currentBoxId: string,
    requirementBoxes: RequirementBox[]
  ) => {
    const selectedActions = requirementBoxes
      .filter((box) => box.id !== currentBoxId && box.action)
      .map((box) => box.action?.value);

    return allActionOptions.filter(
      (option) => !selectedActions.includes(option.value)
    );
  };
  const { data } = useMultiStepFormState('automation') as {
    data: PipelineInfo | undefined;
  };
  const { data: authUser } = useAuthUser();
  const countryCode = authUser?.location?.countryCode;

  const conditionMatchItems = [
    {
      label: t('all_requirements_must_be_met'),
      value: 'ALL',
    },
    {
      label: t('one_requirement_can_be_met'),
      value: 'ANY',
    },
  ];

  const createInitialRequirementBoxes = () => {
    const boxes: RequirementBox[] = [];

    if (autoConditionData?.locationCheckingEnabled) {
      boxes.push({
        id: 'location',
        action: { value: 'location', label: t('location') },
        status: {
          value: autoConditionData.locationCountriesContainsOrNot
            ? 'is'
            : 'isNot',
          label: autoConditionData.locationCountriesContainsOrNot
            ? t('is')
            : t('is_not'),
        },
        value: autoConditionData.locationCountries || [],
      });
    }

    if (autoConditionData?.ageRangeCheckingEnabled) {
      boxes.push({
        id: 'age',
        action: { value: 'age', label: t('age') },
        status: {
          value: autoConditionData.ageRangeIsBetweenOrNot ? 'is' : 'isNot',
          label: autoConditionData.ageRangeIsBetweenOrNot
            ? t('is')
            : t('is_not'),
        },
        value: {
          minAge: autoConditionData.minAge,
          maxAge: autoConditionData.maxAge,
        },
      });
    }

    if (autoConditionData?.phoneNumberCheckingEnabled) {
      boxes.push({
        id: 'phoneNumber',
        action: { value: 'phoneNumber', label: t('phone_number') },
        status: {
          value: autoConditionData.phoneNumberPresentedOrNot ? 'is' : 'isNot',
          label: autoConditionData.phoneNumberPresentedOrNot
            ? t('is')
            : t('is_not'),
        },
        value: null,
      });
    }

    if (autoConditionData?.coverLetterCheckingEnabled) {
      boxes.push({
        id: 'coverLetter',
        action: { value: 'coverLetter', label: t('cover_letter') },
        status: {
          value: autoConditionData.coverLetterPresentedOrNot ? 'is' : 'isNot',
          label: autoConditionData.coverLetterPresentedOrNot
            ? t('is')
            : t('is_not'),
        },
        value: null,
      });
    }

    return boxes;
  };

  const LOCATION_FIELD = {
    cp: 'asyncAutoCompleteWithExtraParams',
    url: geoEndpoints.location.searchCountry,
    normalizer: geoNormalizer.updatedSearchCountry,
    label: t('location'),
    name: 'locationWithExtraParams',
    autoComplete: 'nope',
    rightIconProps: {
      name: 'search',
    },
    visibleRightIcon: true,
    isMulti: true,
    limit: 10,
    showDropDownWithoutEnteringAnything: true,
    initSearchValue: 'a',
    keywords: 'text',
  };

  const handleAddBox = (
    setFieldValue: any,
    requirementBoxes: RequirementBox[]
  ) => {
    const newBox: RequirementBox = {
      id: Date.now().toString(),
      action: null,
      status: null,
      value: null,
    };
    setFieldValue('requirementBoxes', [...requirementBoxes, newBox]);
  };

  const handleRemoveBox = (
    id: string,
    setFieldValue: any,
    requirementBoxes: RequirementBox[]
  ) => {
    setFieldValue(
      'requirementBoxes',
      requirementBoxes.filter((box) => box.id !== id)
    );
  };

  const handleActionChange = (
    id: string,
    action: ActionOption,
    setFieldValue: any,
    requirementBoxes: RequirementBox[]
  ) => {
    setFieldValue(
      'requirementBoxes',
      requirementBoxes.map((box) => (box.id === id ? { ...box, action } : box))
    );
  };

  const handleStatusChange = (
    id: string,
    status: StatusOption,
    setFieldValue: any,
    requirementBoxes: RequirementBox[]
  ) => {
    setFieldValue(
      'requirementBoxes',
      requirementBoxes.map((box) => (box.id === id ? { ...box, status } : box))
    );
  };

  const getFieldsForAction = (actionValue: string) => {
    switch (actionValue) {
      case 'location':
        return [LOCATION_FIELD];
      case 'age':
        return [
          {
            label: t('min_age'),
            name: 'minAge',
            type: 'number',
            cp: 'input',
            inputProps: {
              placeholder: t('min_age'),
            },
          },
          {
            label: t('max_age'),
            name: 'maxAge',
            type: 'number',
            cp: 'input',
            inputProps: {
              placeholder: t('max_age'),
            },
            wrapStyle: 'mt-20',
          },
        ];
      case 'coverLetter':
        return [];
      case 'phoneNumber':
        return [];
      default:
        return [];
    }
  };

  const renderValueField = (box: RequirementBox, formikProps: any) => {
    if (!box.action || !box.status) return null;

    const fields = getFieldsForAction(box.action.value);
    if (fields.length === 0) return null;

    const fieldsWithBoxId = fields.map((field) => {
      const fieldWithBoxId = {
        ...field,
        name: `requirementBox_${box.id}_${field.name}`,
      };

      if (
        autoConditionData &&
        box.id === 'location' &&
        field.name === 'locationWithExtraParams' &&
        autoConditionData.locationCountries?.length > 0
      ) {
        const fieldName = `requirementBox_${box.id}_${field.name}`;
        const currentValue = formikProps.values?.[fieldName];

        if (
          !currentValue ||
          !Array.isArray(currentValue) ||
          currentValue.length === 0
        ) {
          const locationValues = autoConditionData.locationCountries.map(
            (country: any) => ({
              id: country.id,
              label: country.name,
              value: country.code,
              code: country.code,
              countryCode: country.code,
            })
          );

          formikProps.setFieldValue?.(fieldName, locationValues);
        }
      }

      return fieldWithBoxId;
    });

    if (autoConditionData && box.id === 'age') {
      const minAgeFieldName = `requirementBox_${box.id}_minAge`;
      const maxAgeFieldName = `requirementBox_${box.id}_maxAge`;

      if (!formikProps.values?.[minAgeFieldName] && autoConditionData.minAge) {
        formikProps.setFieldValue?.(
          minAgeFieldName,
          autoConditionData.minAge.toString()
        );
      }
      if (!formikProps.values?.[maxAgeFieldName] && autoConditionData.maxAge) {
        formikProps.setFieldValue?.(
          maxAgeFieldName,
          autoConditionData.maxAge.toString()
        );
      }
    }

    return (
      <Flex className="gap-12">
        <Typography className="!text-primaryText !text-base !font-bold">
          {t('requirement')}
        </Typography>
        <DynamicFormBuilder groups={fieldsWithBoxId} />
      </Flex>
    );
  };

  const transformFormData = (values: any) => {
    const requirementBoxes = values.requirementBoxes || [];
    const selectedConditionMatch =
      values.selectedConditionMatch || conditionMatchItems[0];

    const ageBox = requirementBoxes.find(
      (box: RequirementBox) => box.action?.value === 'age'
    );
    const locationBox = requirementBoxes.find(
      (box: RequirementBox) => box.action?.value === 'location'
    );
    const coverLetterBox = requirementBoxes.find(
      (box: RequirementBox) => box.action?.value === 'coverLetter'
    );
    const phoneNumberBox = requirementBoxes.find(
      (box: RequirementBox) => box.action?.value === 'phoneNumber'
    );

    const getBoxValue = (
      box: RequirementBox | undefined,
      fieldName: string
    ) => {
      if (!box) return undefined;

      return values[`requirementBox_${box.id}_${fieldName}`];
    };

    const locationValue = getBoxValue(locationBox, 'locationWithExtraParams');
    const locationCountries = Array.isArray(locationValue)
      ? locationValue
          .map((loc: any) => ({
            id: loc.id || 0,
            name: loc.label || loc.name || '',
            code: loc.code || loc.countryCode || loc.value || '',
          }))
          .filter((country) => country.code)
      : locationValue?.code ||
          locationValue?.countryCode ||
          locationValue?.value
        ? [
            {
              id: locationValue.id || 0,
              name: locationValue.label || locationValue.name || '',
              code:
                locationValue.code ||
                locationValue.countryCode ||
                locationValue.value ||
                '',
            },
          ]
        : [];

    const formValue = {
      toPipelineId: parseInt(data?.id || '0', 10),
      coverLetterCheckingEnabled: !!coverLetterBox,
      coverLetterPresentedOrNot:
        coverLetterBox?.status?.value === 'is' || false,
      phoneNumberCheckingEnabled: !!phoneNumberBox,
      phoneNumberPresentedOrNot:
        phoneNumberBox?.status?.value === 'is' || false,
      ageRangeCheckingEnabled: !!ageBox,
      ageRangeIsBetweenOrNot: ageBox?.status?.value === 'is' || false,
      minAge: getBoxValue(ageBox, 'minAge')
        ? parseInt(getBoxValue(ageBox, 'minAge'), 10)
        : 0,
      maxAge: getBoxValue(ageBox, 'maxAge')
        ? parseInt(getBoxValue(ageBox, 'maxAge'), 10)
        : 0,
      selectedConditionMatch,
      locationCheckingEnabled: !!locationBox,
      locationCountriesContainsOrNot:
        locationBox?.status?.value === 'is' || false,
      locationCountries,
      requirementBoxes,
      triggered: false,
    };

    return formValue;
  };

  const handleDelete = () => {
    if (deleteAutoConditionalMutation && pipelineId) {
      deleteAutoConditionalMutation({ pipelineId });
    }
  };

  const getHeaderProps: SingleDataItem['getHeaderProps'] = () => ({
    title: t('requirements'),
    hideBack: false,
    noCloseButton: true,
    backButtonProps: {
      onClick: () => {
        closeMultiStepForm('automation');
        openMultiStepForm({
          formName: 'automation',
          data,
          type: automationType,
        });
      },
    },
  });

  const renderFooter: SingleDataItem['renderFooter'] = ({
    setStep,
    values,
  }) => {
    const requirementBoxes = values.requirementBoxes || [];

    return (
      <TwoButtonFooter
        submitLabel={t('next')}
        secondaryButtonLabel={t('discard')}
        onSubmitClick={() => {
          const formData = transformFormData(values);

          onFormDataChange(formData);
          setStep((prev) => prev + 1);
        }}
        disabledSubmit={
          !requirementBoxes?.some((box: RequirementBox) => {
            if (
              box.action?.value === 'coverLetter' ||
              box.action?.value === 'phoneNumber'
            ) {
              return box.action && box.status;
            }

            if (box.action && box.status) {
              const fields = getFieldsForAction(box.action.value);
              if (fields.length === 0) return true;

              return fields.some((field) => {
                const fieldName = `requirementBox_${box.id}_${field.name}`;

                return values?.[fieldName];
              });
            }

            return false;
          })
        }
        secondaryButtonOnClick={() => {
          closeMultiStepForm('automation');
        }}
      />
    );
  };

  const getStepHeaderProps: SingleDataItem['getStepHeaderProps'] = () => ({
    title: headerTitle,
    iconProps: {
      name: 'Pipe-move',
      type: 'fal',
    },
  });

  const renderBody: SingleDataItem['renderBody'] = ({
    values,
    setFieldValue,
  }) => {
    const initialRequirementBoxes =
      autoConditionData && !values.requirementBoxes?.length
        ? createInitialRequirementBoxes()
        : [];

    const requirementBoxes = values.requirementBoxes?.length
      ? values.requirementBoxes
      : initialRequirementBoxes;

    const selectedConditionMatch =
      values.selectedConditionMatch ||
      (autoConditionData?.conditionMatchMode
        ? conditionMatchItems.find(
            (item) => item.value === autoConditionData.conditionMatchMode
          )
        : conditionMatchItems[0]) ||
      conditionMatchItems[0];

    if (
      autoConditionData &&
      !values.requirementBoxes?.length &&
      initialRequirementBoxes.length > 0
    ) {
      setFieldValue?.('requirementBoxes', initialRequirementBoxes);
      setFieldValue?.('selectedConditionMatch', selectedConditionMatch);
    }

    if (isLoadingAutoCondition) {
      return <AutoConditionalLoading />;
    }

    return (
      <Flex className="h-full bg-darkSecondary overflow-y-auto">
        <Flex className="gap-20">
          <Flex className="gap-20">
            <AutomationStageType type={automationType} />

            <AutoComplete
              editable={false}
              visibleRightIcon
              // variant="simple-large"
              value={selectedConditionMatch}
              onChangeInput={(value: any) => {
                const option = conditionMatchItems.find(
                  (opt) => opt.value === value.value
                );
                if (option && setFieldValue) {
                  setFieldValue('selectedConditionMatch', option);
                }
              }}
              inputWrapClassName="w-full"
              options={conditionMatchItems}
              // renderItem={({ item }) => (
              //   <Flex
              //     flexDir="row"
              //     alignItems="center"
              //     className="w-full h-[56px] gap-10"
              //   >
              //     <Typography size={16}>{item.label}</Typography>
              //   </Flex>
              // )}
              className="w-full"
              label={t('requirements_logic')}
              // optionsVariant={isMoreThanTablet ? 'dropdown' : 'bottomsheet'}
              displayName={selectedConditionMatch.label}
              onSelect={(item: any) => {
                if (setFieldValue) {
                  setFieldValue('selectedConditionMatch', item);
                }
              }}
            />
          </Flex>

          {!!requirementBoxes?.length && (
            <Flex className="gap-20">
              {requirementBoxes?.map((box: RequirementBox) => (
                <Flex
                  key={box.id}
                  className="gap-20 bg-gray_5 p-20 border border-solid border-techGray_20 rounded-xl"
                >
                  <Flex
                    flexDir="row"
                    className="justify-between items-center w-full"
                  >
                    <Typography className="!text-xl !font-bold !text-smoke_coal">
                      {t('if')}
                    </Typography>
                    <IconButton
                      name="trash"
                      type="fal"
                      size="md20"
                      colorSchema="transparent"
                      color="smoke_coal"
                      iconProps={{
                        color: 'smoke_coal',
                      }}
                      onClick={() =>
                        handleRemoveBox(box.id, setFieldValue, requirementBoxes)
                      }
                    />
                  </Flex>
                  <Flex flexDir="column" className="gap-20">
                    <AutoComplete
                      editable={false}
                      visibleRightIcon
                      variant="simple-large"
                      placeholder={t('action')}
                      value={box.action ?? { value: '', label: '' }}
                      onSelect={(value: any) =>
                        handleActionChange(
                          box.id,
                          value,
                          setFieldValue,
                          requirementBoxes
                        )
                      }
                      inputWrapClassName="w-full"
                      options={getFilteredActionOptions(
                        box.id,
                        requirementBoxes
                      )}
                      displayName={box.action?.label}
                      rightIconClassName=""
                      className="w-full"
                      optionsVariant={
                        isMoreThanTablet ? 'dropdown' : 'bottomsheet'
                      }
                    />
                    {box.action && (
                      <AutoComplete
                        editable={false}
                        visibleRightIcon
                        variant="simple-large"
                        placeholder={t('status')}
                        value={box.status ?? { label: '', value: '' }}
                        onSelect={(value: any) =>
                          handleStatusChange(
                            box.id,
                            value,
                            setFieldValue,
                            requirementBoxes
                          )
                        }
                        inputWrapClassName="w-full"
                        displayName={box.status?.label}
                        options={typeOptions}
                        rightIconClassName=""
                        className="w-full"
                        optionsVariant={
                          isMoreThanTablet ? 'dropdown' : 'bottomsheet'
                        }
                      />
                    )}
                    {box.action &&
                      box.status &&
                      renderValueField(box, { values, setFieldValue })}
                  </Flex>
                </Flex>
              ))}
            </Flex>
          )}
          {requirementBoxes?.length <= 3 && (
            <Flex flexDir="row" className="justify-start">
              <IconButton
                name="plus"
                type="far"
                size="md"
                colorSchema="graySecondary"
                variant="circle"
                onClick={() => handleAddBox(setFieldValue, requirementBoxes)}
                tooltip={t('add_requirement')}
              />
            </Flex>
          )}
        </Flex>
      </Flex>
    );
  };

  const stepData: Array<SingleDataItem> = [
    {
      stepKey: '1',
      getHeaderProps,
      getStepHeaderProps,
      renderBody,
      renderFooter,
    },
  ];

  return stepData;
}
