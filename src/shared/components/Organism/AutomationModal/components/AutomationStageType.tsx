import { useState } from 'react';
import {
  openMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import AutoComplete from '@shared/uikit/AutoComplete';
import IconButton from '@shared/uikit/Button/IconButton';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import useMedia from '@shared/uikit/utils/useMedia';
import { useStageOptions } from './useStageOptions';
import type { StageOption } from './types';
import type { AutomationModalType } from '@shared/hooks/useMultiStepForm';

type AutomationStageTypeProps = {
  type: AutomationModalType;
};

export function AutomationStageType({ type }: AutomationStageTypeProps) {
  const automationState = useMultiStepFormState('automation');
  const { isMoreThanTablet } = useMedia();
  const stageOptions = useStageOptions();

  const initialStage =
    stageOptions.find(
      (opt) =>
        opt.value?.toLowerCase() === automationState?.data?.type?.toLowerCase()
    ) || stageOptions[0];

  const [selectedStage, setSelectedStage] = useState<StageOption>(initialStage);

  return (
    <AutoComplete
      editable={false}
      visibleRightIcon
      variant="simple"
      value={selectedStage}
      onChangeInput={(value: StageOption) => {
        const option = stageOptions.find((opt) => opt.value === value.value);

        if (option) {
          setSelectedStage(option);
        }
      }}
      leftIcon={
        <IconButton
          iconProps={{ color: selectedStage?.iconColor }}
          name={selectedStage?.icon}
          type="far"
          variant="rectangle"
          size="sm13"
          colorSchema="secondary"
          className="!bg-darkSecondary_hover"
        />
      }
      leftIconClassName="ml-12"
      rightIconClassName="!right-0"
      inputWrapClassName="w-full !bg-transparent"
      options={stageOptions}
      renderItem={({ item }) => (
        <Flex
          flexDir="row"
          alignItems="center"
          className="w-full h-[40px] gap-10"
        >
          <IconButton
            iconProps={{ color: item?.iconColor }}
            name={item?.icon}
            type="far"
            variant="rectangle"
            size="sm13"
          />
          <Typography size={16}>{item.label}</Typography>
        </Flex>
      )}
      className="w-full"
      rightIconProps={{ colorSchema: 'transparent' }}
      optionsVariant={isMoreThanTablet ? 'dropdown' : 'bottomsheet'}
      displayName={selectedStage.label}
      onSelect={(item: StageOption) => {
        setSelectedStage(item);
        openMultiStepForm({
          formName: 'automation',
          data: {
            ...automationState?.data,
            id: item?.id,
            type: item?.value,
            title: item?.label,
          },
          type,
        });
      }}
    />
  );
}
