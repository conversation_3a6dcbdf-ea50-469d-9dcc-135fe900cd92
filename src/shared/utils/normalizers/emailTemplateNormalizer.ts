import type { TemplateFormData } from '@shared/components/Organism/AutomationModal/types/template.types';
import type { EmailTemplateResponse } from '@shared/utils/api/template';

export type EmailTemplate = {
  id: number;
  createdDate: string;
  userId: number;
  pageId: number;
  title: string;
  subject: string;
  message: string;
  timeDelay: string;
  fileIds: number[];
  hasFollowup: boolean;
  followupTitle: string;
  followupMessage: string;
  followupPeriod: string;
  followupFileIds: number[];
  default: boolean;
  isGeneral: boolean;
};

export type NormalizedEmailTemplate = {
  title: string;
  subject: string;
  message: string;
  default?: boolean;
  isGeneral: boolean;
};

export const getEmailTemplateNormalizer = (
  data: EmailTemplateResponse
): TemplateFormData => ({
  attachments: data?.fileIds,
  delay: data?.timeDelay,
  followupAttachments: data?.followupFileIds,
  followupMessage: data?.followupMessage,
  followupPeriod: data?.followupPeriod,
  followupTitle: data?.followupTitle,
  hasFollowup: data?.hasFollowup,
  message: data?.message,
  subject: data?.subject,
  templateName: data?.title,
  default: data?.default,
});
