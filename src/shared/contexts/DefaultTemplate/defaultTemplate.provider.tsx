'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';
import type { NormalizedMeetingTemplate } from '@shared/hooks/schedules/useMeetingTemplates';
import type { ReactNode } from 'react';

interface DefaultTemplateContextType {
  defaultTemplateId: string | null;
  setDefaultTemplateId: (templateId: string | null) => void;
  getDefaultTemplate: (
    templates: NormalizedMeetingTemplate[]
  ) => NormalizedMeetingTemplate | undefined;
}

const DefaultTemplateContext = createContext<
  DefaultTemplateContextType | undefined
>(undefined);

interface DefaultTemplateProviderProps {
  children: ReactNode;
}

export const DefaultTemplateProvider: React.FC<
  DefaultTemplateProviderProps
> = ({ children }) => {
  const [defaultTemplateId, setDefaultTemplateIdState] = useState<
    string | null
  >(null);

  const setDefaultTemplateId = useCallback((templateId: string | null) => {
    setDefaultTemplateIdState(templateId);
  }, []);

  const getDefaultTemplate = useCallback(
    (templates: NormalizedMeetingTemplate[]) => {
      if (!defaultTemplateId) return undefined;

      return templates.find((template) => template.id === defaultTemplateId);
    },
    [defaultTemplateId]
  );

  const value: DefaultTemplateContextType = {
    defaultTemplateId,
    setDefaultTemplateId,
    getDefaultTemplate,
  };

  return (
    <DefaultTemplateContext.Provider value={value}>
      {children}
    </DefaultTemplateContext.Provider>
  );
};

export const useDefaultTemplate = (): DefaultTemplateContextType => {
  const context = useContext(DefaultTemplateContext);
  if (context === undefined) {
    throw new Error(
      'useDefaultTemplate must be used within a DefaultTemplateProvider'
    );
  }

  return context;
};
