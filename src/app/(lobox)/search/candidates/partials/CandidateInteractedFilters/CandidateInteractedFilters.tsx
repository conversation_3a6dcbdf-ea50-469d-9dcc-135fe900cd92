import Flex from '@shared/uikit/Flex';
import Switch from '@shared/uikit/Switch';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import useTranslation from '@shared/utils/hooks/useTranslation';

const CandidateInteractedFilters = () => {
  const { handleChangeParams, allParams } = useCustomParams();
  const { t } = useTranslation();
  const items = [
    {
      value: 'hasResumeInteraction',
      label: 'resume',
    },
    {
      value: 'hasNoteInteraction',
      label: 'notes',
    },
    {
      value: 'hasTodoInteraction',
      label: 'todos',
    },
    {
      value: 'hasMeetingInteraction',
      label: 'meetings',
    },
    {
      value: 'hasDocumentInteraction',
      label: 'documents',
    },
    {
      value: 'hasReviewInteraction',
      label: 'reviews',
    },
    {
      value: 'hasSkillBoardInteraction',
      label: 'skill_board',
    },
    {
      value: 'hasEmailInteraction',
      label: 'emails',
    },
    {
      value: 'hasAssessmentInteraction',
      label: 'assessments',
    },
  ];

  return (
    <Flex className="!flex-row items-center   h-[56px] bg-popOverBg_white">
      <Flex className="!flex-row items-center gap-8 ">
        {items.map(({ value, label }) => {
          const hasValue = allParams?.[value] === 'true';

          return (
            <Flex key={value} className="bg-brand_10 px-12 h-32 rounded-full">
              <Switch
                label={t(label)}
                labelProps={{ font: '400', color: 'smoke_coal' }}
                className="!p-0 gap-12"
                value={hasValue}
                onChange={() => {
                  handleChangeParams({
                    add: !hasValue ? { [value]: 'true' } : undefined,
                    remove: hasValue
                      ? [value, 'currentEntityId']
                      : ['currentEntityId'],
                  });
                }}
              />
            </Flex>
          );
        })}
      </Flex>
    </Flex>
  );
};

export default CandidateInteractedFilters;
