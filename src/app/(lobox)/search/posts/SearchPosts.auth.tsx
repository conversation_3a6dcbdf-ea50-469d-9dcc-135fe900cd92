'use client';

import React from 'react';
import { usePostsFilterGroups } from '@shared/hooks/searchFilters/usePostsFilterGroups';
import useSearchResultWithFilters from '@shared/hooks/searchFilters/useSearchResultWithFilters';
import BaseButton from '@shared/uikit/Button/BaseButton';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import SearchListWithDetailsLayout from 'shared/components/layouts/SearchListWithDetailsLayout';
import FeedCard from 'shared/components/Organism/FeedCard';
import { FeedElementProvider } from 'shared/components/Organism/FeedCard/Context/feedElement.provider';
import SearchList from 'shared/components/Organism/SearchList';
import SearchPostsDetails from 'shared/components/Organism/SearchPosts/SearchPosts.details';
import SearchPostsItem from 'shared/components/Organism/SearchPosts/SearchPosts.item';
import useMedia from 'shared/uikit/utils/useMedia';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useUpdateInfinityData from 'shared/utils/hooks/useUpdateInfinityData';
import classes from './SearchPosts.module.scss';
import type { IFeedElement } from '@shared/types/feedElement';

const SearchPostsAuth = () => {
  const { t } = useTranslation();
  const { isTabletAndLess } = useMedia();
  const groups = usePostsFilterGroups();
  const { handleChangeParams } = useCustomParams();
  const {
    totalElements,
    totalPages,
    content = [],
    isEmpty,
    setPage,
    isFetching,
    queryKey,
    isFetchingFilters,
  } = useSearchResultWithFilters({ entity: 'posts' });

  const { remove, replace } = useUpdateInfinityData(queryKey as any);
  const handleUpdatePost = (feed: IFeedElement) => {
    replace(feed);
  };
  const handleDeletePost = (feed: IFeedElement) => {
    remove(feed.id);
  };

  return (
    <SearchListWithDetailsLayout
      groups={groups}
      isLoading={isFetchingFilters}
      isTotalyEmpty={Boolean(isEmpty && !isFetching)}
      sectionTotalEmpty={
        <EmptySectionInModules
          isFullParent
          isFullWidth
          title={t('no_result_f')}
          text={t('try_refining_search')}
        />
      }
      listComponent={
        <SearchList
          entity="posts"
          title={t('posts')}
          isLoading={isFetching}
          totalElements={totalElements}
          data={content}
          totalPages={totalPages}
          onPageChange={(p) => {
            setPage(String(p));
          }}
          noTopBottomPadding
          renderItem={(feedElement, index, itemProps) =>
            isTabletAndLess ? (
              <FeedCard
                key={feedElement.post.id + index}
                feedElement={feedElement}
                className={classes.postDetailRoot}
                onReplacePost={handleUpdatePost}
                onDeletePost={handleDeletePost}
              />
            ) : (
              <FeedElementProvider
                initialValue={{
                  feedElement,
                  variant: 'search-list-card',
                }}
              >
                <BaseButton
                  onClick={() =>
                    handleChangeParams({
                      add: { currentEntityId: feedElement.post.id },
                    })
                  }
                >
                  <SearchPostsItem itemProps={itemProps} />
                </BaseButton>
              </FeedElementProvider>
            )
          }
        />
      }
      detailsComponent={<SearchPostsDetails isFetching={isFetching} />}
    />
  );
};

export default SearchPostsAuth;
