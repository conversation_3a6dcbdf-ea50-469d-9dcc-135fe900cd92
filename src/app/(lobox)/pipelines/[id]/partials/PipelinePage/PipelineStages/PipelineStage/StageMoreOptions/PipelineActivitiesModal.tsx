import debounce from 'lodash/debounce';
import { useCallback, useState, type FC } from 'react';
import ActivityItem from '@shared/components/molecules/ActivityItem';
import ActivityItemSkeleton from '@shared/components/molecules/ActivityItem/ActivityItemSkeleton';
import CardBadge from '@shared/components/molecules/CardBadge/CardBadge';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import Flex from '@shared/uikit/Flex';
import FixedRightSideModalDialog from '@shared/uikit/Modal/FixedRightSideModalDialog';
import type { PipelineInfo } from '@shared/types/pipelineProps';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import SearchInputV2 from '@shared/uikit/SearchInputV2';
import { searchProjectActivities } from '@shared/utils/api/project';
import { QueryKeys } from '@shared/utils/constants';
import useTranslation from '@shared/utils/hooks/useTranslation';
import ViewPortList from 'shared/uikit/ViewPortList';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import type { ActivityProps } from '@shared/types/activityProps';

interface PipelineActivitiesModalProps {
  stage: PipelineInfo;
  onClose: VoidFunction;
}

const Footer = () => (
  <Flex className="gap-16">
    {new Array(20).fill(0).map((_, index) => (
      <ActivityItemSkeleton key={index} />
    ))}
  </Flex>
);

const PipelineActivitiesModal: FC<PipelineActivitiesModalProps> = (props) => {
  const { onClose, stage } = props;
  const { t } = useTranslation();
  const [text, setText] = useState('');
  const [searchText, setSearchText] = useState('');
  const debounceFn = useCallback(
    debounce((txt: string) => setText(txt), 700),
    [setText]
  );

  const { data, isLoading, hasNextPage, fetchNextPage } =
    useInfiniteQuery<ActivityProps>(
      [QueryKeys.getPipelineActivities, stage.id, text],
      {
        func: ({ page }) =>
          searchProjectActivities({
            page,
            size: 20,
            pipelineId: stage.id,
            text,
          }),
      }
    );

  const onSearch = (value: string) => {
    debounceFn(value);
    setSearchText(value);
  };

  const renderItem = useCallback(
    (index: number, activity: ActivityProps) => (
      <ActivityItem item={activity} classNames={{ root: 'mb-12' }} />
    ),
    []
  );

  return (
    <FixedRightSideModalDialog wide onClose={onClose}>
      <ModalHeaderSimple
        title={t('activities')}
        closeButtonProps={{
          onClick: onClose,
        }}
        belowContent={
          <Flex className="w-full px-20 py-12 bg-gray_5">
            <CardBadge
              classNames={{
                badge: '!w-full px-12 py-8 !gap-8',
              }}
              onChange={() => {}}
              value={t(stage.title)}
              iconsDetails={{
                iconSize: 12,
                iconColor: stage.color,
                iconName: 'circle-s',
                iconClassName: 'p-[4px] bg-darkSecondary_hover rounded-[4px]',
              }}
              tooltipProps={{
                triggerWrapperClassName: '!w-full',
              }}
            />
          </Flex>
        }
      />
      <ModalBody className="w-full flex flex-col">
        <SearchInputV2
          onChange={onSearch}
          placeholder={t('search_activities')}
          value={searchText}
          className="mb-16"
        />

        {isLoading ? (
          <Footer />
        ) : data?.length > 0 ? (
          <ViewPortList
            useRelativeScroller
            style={{ height: '100%' }}
            className="flex-1"
            data={data}
            endReached={() => hasNextPage && fetchNextPage()}
            increaseViewportBy={200}
            itemContent={renderItem}
            components={{
              List: Flex,
              Footer: () => (isLoading || hasNextPage ? <Footer /> : null),
            }}
          />
        ) : (
          <EmptySearchResult
            title={t('no_activities')}
            sectionMessage={t('no_activities_desc')}
            className=""
          />
        )}
      </ModalBody>
    </FixedRightSideModalDialog>
  );
};

export default PipelineActivitiesModal;
